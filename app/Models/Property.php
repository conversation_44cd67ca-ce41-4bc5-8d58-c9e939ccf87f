<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Property extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'property_sub_type_id',
        'listing_type',
        'title',
        'description',
        'price',
        'currency',
        'address_line_1',
        'city',
        'state_region',
        'zip_code',
        'bedrooms',
        'bathrooms',
        'square_footage',
        'latitude',
        'longitude',
        'lister_type',
    ];

    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'is_featured' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function favoritedBy()
    {
        return $this->belongsToMany(User::class, 'favorites');
    }

    public function propertySubType()
    {
        return $this->belongsTo(PropertySubType::class);
    }

    public function amenities()
    {
        return $this->belongsToMany(Amenity::class);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
              ->width(100)
              ->height(100)
              ->sharpen(10);

        $this->addMediaConversion('preview')
              ->width(400)
              ->height(300)
              ->sharpen(10);
    }
}

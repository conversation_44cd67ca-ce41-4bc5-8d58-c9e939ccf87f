<?php

namespace App\Notifications;

use App\Models\Property;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class NewPropertyMatched extends Notification implements ShouldBroadcast
{
    use Queueable;

    public $property;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Property  $property
     * @return void
     */
    public function __construct(Property $property)
    {
        $this->property = $property;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['broadcast', 'database'];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\BroadcastMessage
     */
    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'property_id' => $this->property->id,
            'title' => $this->property->title,
            'price' => $this->property->price,
            'location' => $this->property->location,
            'message' => "A new property matching your criteria is available: {$this->property->title}",
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'property_id' => $this->property->id,
            'title' => $this->property->title,
            'price' => $this->property->price,
            'location' => $this->property->location,
            'message' => "A new property matching your criteria is available: {$this->property->title}",
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Property;
use App\Services\PropertyRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class PropertyController extends Controller
{
    protected PropertyRepository $propertyRepository;

    public function __construct(PropertyRepository $propertyRepository)
    {
        $this->propertyRepository = $propertyRepository;
    }

    /**
     * Display a listing of the properties.
     */
    public function index()
    {
        $properties = Cache::remember('all_published_properties', 60, function () {
            return $this->propertyRepository->publishedOnly(
                $this->propertyRepository->baseQuery()
            )->get();
        });

        return view('properties.index', compact('properties'));
    }

    /**
     * Display the specified property.
     */
    public function show(Property $property)
    {
        if ($property->status !== 'published' && (!Auth::check() || Auth::id() !== $property->user_id)) {
            abort(404); // Only published properties are publicly viewable, or owner can view drafts
        }

        // Eager load relationships for the property detail view
        $property->load(['user', 'propertySubType.propertyType', 'media', 'amenities']);

        return view('properties.show', compact('property'));
    }




}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('search_latitude', 10, 8)->nullable()->after('search_radius_km');
            $table->decimal('search_longitude', 11, 8)->nullable()->after('search_latitude');
            $table->string('notify_property_type')->nullable()->after('search_longitude');
            $table->decimal('notify_budget_min', 15, 2)->nullable()->after('notify_property_type');
            $table->decimal('notify_budget_max', 15, 2)->nullable()->after('notify_budget_min');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['search_latitude', 'search_longitude', 'notify_property_type', 'notify_budget_min', 'notify_budget_max']);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('budget_min')->nullable()->after('remember_token');
            $table->integer('budget_max')->nullable()->after('budget_min');
            $table->integer('search_radius_km')->nullable()->after('budget_max');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['budget_min', 'budget_max', 'search_radius_km']);
        });
    }
};

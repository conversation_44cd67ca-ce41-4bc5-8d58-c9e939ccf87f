<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->decimal('lot_size_acres', 8, 2)->nullable()->after('square_footage');
            $table->string('zoning_details')->nullable()->after('lot_size_acres');
            $table->boolean('road_access')->default(false)->after('zoning_details');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->dropColumn(['lot_size_acres', 'zoning_details', 'road_access']);
        });
    }
};

<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Assign roles to existing users
        $admin = User::where('email', '<EMAIL>')->first();
        if ($admin) {
            $admin->assignRole('admin');
        }

        $lister1 = User::where('email', '<EMAIL>')->first();
        if ($lister1) {
            $lister1->assignRole('agent');
        }

        $lister2 = User::where('email', '<EMAIL>')->first();
        if ($lister2) {
            $lister2->assignRole('agent');
        }

        $seeker = User::where('email', '<EMAIL>')->first();
        if ($seeker) {
            $seeker->assignRole('seeker');
            $seeker->update([
                'budget_min' => 100000,
                'budget_max' => 500000,
                'search_radius_km' => 20,
            ]);
        }

        // Assign roles to additional factory-created users
        User::whereNotIn('email', ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'])
            ->each(function ($user) {
                // Assign 'agent' role to some, 'seeker' to others
                if (rand(0, 1)) {
                    $user->assignRole('agent');
                } else {
                    $user->assignRole('seeker');
                    $user->update([
                        'budget_min' => rand(50000, 200000),
                        'budget_max' => rand(250000, 1000000),
                        'search_radius_km' => rand(5, 50),
                    ]);
                }
            });
    }
}

<div class="relative">
    <button wire:click="loadNotifications" class="relative focus:outline-none">
        <svg class="w-6 h-6 text-gray-500 hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17l-3-3m0 0l-3 3m3-3v-5m-8 5h16"></path>
        </svg>
        @if($unreadCount > 0)
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-2 py-1">{{ $unreadCount }}</span>
        @endif
    </button>

    <div x-data="{ open: false }" @click.away="open = false" class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50" x-show="open" @click="open = !open">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-700">Notifications</h3>
            @if($unreadCount > 0)
                <button wire:click="markAllAsRead" class="text-sm text-blue-500 hover:text-blue-700">Mark all as read</button>
            @endif
        </div>
        <div class="max-h-96 overflow-y-auto">
            @if($notifications->isEmpty())
                <p class="p-4 text-center text-gray-500">No notifications yet.</p>
            @else
                @foreach($notifications as $notification)
                    <div class="p-4 border-b border-gray-100 {{ $notification->unread() ? 'bg-blue-50' : 'bg-white' }}" wire:key="{{ $notification->id }}">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm font-medium text-gray-800">{{ $notification->data['message'] ?? 'New notification' }}</p>
                                <p class="text-xs text-gray-500">{{ $notification->created_at->diffForHumans() }}</p>
                            </div>
                            @if($notification->unread())
                                <button wire:click="markAsRead('{{ $notification->id }}')" class="text-xs text-blue-500 hover:text-blue-700">Mark as read</button>
                            @endif
                        </div>
                        @if(isset($notification->data['property_id']))
                            <a href="{{ route('properties.show', $notification->data['property_id']) }}" class="text-xs text-blue-600 hover:underline">View Property</a>
                        @endif
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    if (window.Echo) {
        window.Echo.private(`App.Models.User.${window.Laravel.userId}`)
            .notification((notification) => {
                Livewire.emit('refreshNotifications');
            });
    }
});
</script>

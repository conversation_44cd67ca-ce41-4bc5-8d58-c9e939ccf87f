<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ $title ?? config('app.name') }}</title>

        <link rel="icon" href="/favicon.ico" sizes="any">
        <link rel="icon" href="/favicon.svg" type="image/svg+xml">
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">

        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

        <!-- FontAwesome Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

        @vite(['resources/css/app.css'])
        @fluxAppearance
        @livewireStyles
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    </head>
    <body class="bg-gray-50 flex flex-col min-h-screen" x-data="{ 
        currentPage: '{{ request()->path() }}', 
        showMobileMenu: false,
        viewMode: 'grid', // Default view mode
        
        isActive(path) {
            return this.currentPage === path || this.currentPage.startsWith(path + '/');
        }
    }"
    x-on:layout-view-updated.window="viewMode = $event.detail.view">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="{{ route('home') }}" class="flex items-center space-x-2" wire:navigate>
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Lokus
                            </h1>
                        </a>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="showMobileMenu = !showMobileMenu" class="text-gray-700 hover:text-blue-600 p-2">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="{{ route('properties.index') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>Properties</a>
                    @auth
                        <livewire:user-notifications />
                        @if (auth()->user()->hasRole('agent'))
                            <a href="{{ route('agent.properties.index') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>My Properties</a>
                            <a href="{{ route('agent.properties.create') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>Create Listing</a>
                        @endif
                        @if (auth()->user()->hasRole('admin'))
                            <a href="{{ route('admin.dashboard') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>Admin</a>
                        @endif
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>Dashboard</a>
                        @if (auth()->user()->hasRole('agent') || auth()->user()->hasRole('developer') || auth()->user()->hasRole('owner'))
                            <a href="{{ route('profile.edit') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>Profile</a>
                        @endif
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                                Logout
                            </button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" wire:navigate>Sign In</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors" wire:navigate>Register</a>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div class="md:hidden" x-show="showMobileMenu" x-transition>
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="{{ route('properties.index') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>Properties</a>
                @auth
                    @if (auth()->user()->hasRole('agent'))
                        <a href="{{ route('agent.properties.index') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>My Properties</a>
                        <a href="{{ route('agent.properties.create') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>Create Listing</a>
                    @endif
                    @if (auth()->user()->hasRole('admin'))
                        <a href="{{ route('admin.dashboard') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>Admin</a>
                    @endif
                    <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>Dashboard</a>
                    @if (auth()->user()->hasRole('agent') || auth()->user()->hasRole('developer') || auth()->user()->hasRole('owner'))
                        <a href="{{ route('profile.edit') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>Profile</a>
                    @endif
                    <form method="POST" action="{{ route('logout') }}" class="block">
                        @csrf
                        <button type="submit" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium w-full text-left">
                            Logout
                        </button>
                    </form>
                @else
                    <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium" wire:navigate>Sign In</a>
                    <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white block px-3 py-2 text-base font-medium rounded-lg" wire:navigate>Register</a>
                @endauth
            </div>
        </div>
    </nav>

        <!-- Page Content -->
        <main class="flex-grow">
            {{ $slot }}
        </main>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white py-12 mt-auto">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div class="col-span-1 md:col-span-2">
                        <h3 class="text-2xl font-bold mb-4">Lokus</h3>
                        <p class="text-gray-300 mb-4">Your trusted platform for finding and listing properties. Connect with property seekers and agents in your area.</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2">
                            <li><a href="{{ route('properties.index') }}" class="text-gray-300 hover:text-white transition-colors">Browse Properties</a></li>
                            <li><a href="{{ route('register') }}" class="text-gray-300 hover:text-white transition-colors">List Property</a></li>
                            <li><a href="{{ route('login') }}" class="text-gray-300 hover:text-white transition-colors">Sign In</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Contact</h4>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-envelope mr-2"></i><EMAIL></li>
                            <li><i class="fas fa-phone mr-2"></i>+****************</li>
                        </ul>
                    </div>
                </div>
                <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                    <p>&copy; {{ date('Y') }} Lokus. All rights reserved.</p>
                </div>
            </div>
        </footer>


        @livewireScripts
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

        <!-- Laravel configuration for JavaScript -->
        <script>
            window.Laravel = {
                @auth
                userId: {{ auth()->id() }},
                @endauth
            };
        </script>

        @stack('scripts')
    </body>
</html>
